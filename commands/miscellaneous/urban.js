const { EmbedBuilder } = require('discord.js');
const { embeds } = require('../../core/embeds');
const { colors } = require('../../config/setup');
const { runHelpCommand } = require('../../utils/commandProcessor');

module.exports = {
  name: "urban",
  aliases: ['ud'],
  description: "Get the Urban Dictionary definition of a word or phrase",
  usage: '{guildprefix}ud <word/phrase>',
  run: async (client, message, args) => {
    try {
      if (!args[0]) {
        return runHelpCommand(message, 'ud');
      }

      const term = args.join(' ');

      // Use Urban Dictionary API
      const response = await fetch(`https://api.urbandictionary.com/v0/define?term=${encodeURIComponent(term)}`);
      
      if (!response.ok) {
        throw new Error(`API returned ${response.status}`);
      }

      const data = await response.json();
      
      if (!data.list || data.list.length === 0) {
        return embeds.warn(message, `No Urban Dictionary definition found for **${term}**`);
      }

      // Get the top definition (most upvoted)
      const definition = data.list[0];

      // Clean up the definition text (remove brackets)
      const cleanDefinition = definition.definition.replace(/\[|\]/g, '');
      const cleanExample = definition.example ? definition.example.replace(/\[|\]/g, '') : null;

      // Build embed
      const embed = new EmbedBuilder()
        .setColor(colors.embed)
        .setTitle(`🏙️ ${definition.word}`);

      // Add definition
      let definitionText = cleanDefinition;
      
      // Truncate if too long
      if (definitionText.length > 1000) {
        definitionText = definitionText.substring(0, 997) + '...';
      }

      embed.addFields({
        name: 'Definition',
        value: definitionText
      });

      // Add example if available
      if (cleanExample) {
        let exampleText = cleanExample;
        
        // Truncate if too long
        if (exampleText.length > 1000) {
          exampleText = exampleText.substring(0, 997) + '...';
        }

        embed.addFields({
          name: 'Example',
          value: exampleText
        });
      }

      // Add vote counts
      const voteText = "``👍 ${definition.thumbs_up} | 👎 ${definition.thumbs_down}``";
      embed.addFields({
        name: 'Votes',
        value: voteText,
        inline: true
      });

      // Add author and date
      const authorText = "``By ${definition.author}``";
      embed.addFields({
        name: 'Author',
        value: authorText,
        inline: true
      });

      // Add permalink
      embed.setFooter({ text: `Urban Dictionary • one ou` });

      return message.channel.send({ embeds: [embed] });

    } catch (error) {
      console.error('Error in ud command:', error);
      return embeds.deny(message, 'An error occurred while fetching the Urban Dictionary definition. Please try again.');
    }
  }
};
