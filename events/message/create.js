// Message handler for command processing, autoreact, and autoresponder

module.exports = {
  name: 'messageCreate',
  async execute(message) {
    // Skip bots
    if (message.author.bot) return;

    // Skip if no guild
    if (!message.guild) return;

    // Process bot mention with link first (highest priority)
    await processBotMentionDownload(message);

    // Process autoreacts
    await processAutoreact(message);

    // Process autoresponders
    await processAutoresponder(message);

    // Process swear tracking
    await processSwearTracking(message);

    // Process commands last
    await processCommand(message);
  },
};

async function processAutoreact(message) {
  try {
    // Skip if message is empty or only whitespace
    if (!message.content || !message.content.trim()) return;

    const guildCache = require('../../database/cache/models/guild');

    // Get all autoreacts for this guild using cache system
    const autoreacts = await guildCache.getAutoReacts(message.guild.id);

    if (!autoreacts || autoreacts.length === 0) return;

    const messageContent = message.content.toLowerCase();

    // Check each autoreact trigger
    for (const autoreact of autoreacts) {
      // Check if the message contains the trigger word
      if (messageContent.includes(autoreact.word.toLowerCase())) {
        try {
          let emojiToReact;

          if (autoreact.isCustom) {
            // For custom emojis, check if it still exists in the guild
            const emoji = message.guild.emojis.cache.get(autoreact.emojiId);
            if (emoji) {
              emojiToReact = emoji;
            } else {
              // Remove invalid emoji using cache system
              await guildCache.removeAutoReact(message.guild.id, autoreact.word);
              continue;
            }
          } else {
            // For unicode emojis
            emojiToReact = autoreact.emojiName;
          }

          // React to the message
          await message.react(emojiToReact);
        } catch (error) {
          // Silent fail for individual reactions
          // Could be due to missing permissions, invalid emoji, etc.
        }
      }
    }
  } catch (error) {
    // Silent fail for autoreact system
  }
}

async function processAutoresponder(message) {
  try {
    // Skip if message is empty or only whitespace
    if (!message.content || !message.content.trim()) return;

    const guildCache = require('../../database/cache/models/guild');

    // Get all autoresponders for this guild using cache system
    const autoresponders = await guildCache.getAutoResponders(message.guild.id);

    if (!autoresponders || autoresponders.length === 0) return;

    const messageContent = message.content;

    // Check each autoresponder trigger
    for (const autoresponder of autoresponders) {
      let triggered = false;

      if (autoresponder.strict) {
        // Strict mode: exact word match (case insensitive)
        const words = messageContent.toLowerCase().split(/\s+/);
        triggered = words.includes(autoresponder.trigger.toLowerCase());
      } else {
        // Non-strict mode: contains trigger anywhere in message (case insensitive)
        triggered = messageContent.toLowerCase().includes(autoresponder.trigger.toLowerCase());
      }

      if (triggered) {
        try {
          // Delete original message if delete option is enabled
          if (autoresponder.delete) {
            try {
              await message.delete();
            } catch (error) {
              // Silent fail if can't delete (missing permissions, etc.)
            }
          }

          // Send response
          let responseMessage;
          if (autoresponder.reply) {
            responseMessage = await message.reply(autoresponder.response);
          } else {
            responseMessage = await message.channel.send(autoresponder.response);
          }

          // Self-destruct if enabled
          if (autoresponder.selfDestruct > 0) {
            setTimeout(async () => {
              try {
                await responseMessage.delete();
              } catch (error) {
                // Silent fail if message was already deleted
              }
            }, autoresponder.selfDestruct * 1000);
          }

          // Only trigger one autoresponder per message
          break;

        } catch (error) {
          // Silent fail for individual autoresponder
        }
      }
    }
  } catch (error) {
    // Silent fail for autoresponder system
  }
}

async function processCommand(message) {
  try {
    // Process commands with new prefix system
    const { processCommand } = require('../../utils/commandProcessor');
    await processCommand(message, false);
  } catch (error) {
    // Silent fail for commands
  }
}

async function processBotMentionDownload(message) {
  try {
    // Skip if message is empty or only whitespace
    if (!message.content || !message.content.trim()) return;

    const messageContent = message.content.toLowerCase().trim();

    // Check if message starts with bot name variations
    const botNames = ['adore', message.client.user.username.toLowerCase()];
    const startsWithBotName = botNames.some(name =>
      messageContent.startsWith(name + ' ') ||
      messageContent.startsWith(name + '\n') ||
      messageContent === name
    );

    // Also check for @mentions as fallback
    const isMentioned = message.mentions.has(message.client.user);

    // Must start with bot name or be mentioned
    if (!startsWithBotName && !isMentioned) return;

    // Extract URLs from the message
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    const urls = message.content.match(urlRegex);

    if (!urls || urls.length === 0) return;

    // Check if any URL is from supported platforms
    const supportedUrl = urls.find(url =>
      url.match(/instagram\.com\/(p|reel|tv|stories)/) ||
      url.match(/tiktok\.com/)
    );

    if (!supportedUrl) return;

    // Import the download functions
    const { handleInstagramDownload, handleTikTokDownload } = require('../utility/downloadHandler');

    try {
      let success = false;

      if (supportedUrl.match(/instagram\.com\/(p|reel|tv|stories)/)) {
        success = await handleInstagramDownload(message, supportedUrl);
      } else if (supportedUrl.match(/tiktok\.com/)) {
        success = await handleTikTokDownload(message, supportedUrl);
      }

      // If download failed, react with eyes emoji
      if (!success) {
        await message.react('👀');
      }

    } catch (downloadError) {
      // If any error occurs, react with eyes emoji
      try {
        await message.react('👀');
      } catch (reactionError) {
        // Silent fail if can't react
      }
    }

  } catch (error) {
    // Silent fail for bot mention download system
  }
}

async function processSwearTracking(message) {
  try {
    // Skip bots
    if (message.author.bot) return;

    // Skip if message is empty or only whitespace
    if (!message.content || !message.content.trim()) return;

    // Skip if message is a command (starts with prefix)
    const prefixCache = require('../../database/cache/models/prefix');
    const guildPrefix = await prefixCache.getGuildPrefix(message.guild.id);
    const userPrefix = await prefixCache.getUserPrefix(message.author.id);

    const content = message.content.trim();
    if (content.startsWith(guildPrefix) || (userPrefix && content.startsWith(userPrefix))) return;

    // Import swear detection function
    const { detectSwears } = require('../../commands/service/swear');

    // Detect swears in the message
    const foundSwears = detectSwears(message.content);

    if (foundSwears.length === 0) return;

    // Ensure guild data exists
    const { ensureGuildData } = require('../../database');
    await ensureGuildData(message.guild.id);

    // Track each unique swear found
    const guildCache = require('../../database/cache/models/guild');

    for (const swear of foundSwears) {
      await guildCache.addSwear(message.guild.id, message.author.id, swear);
    }

  } catch (error) {
    // Log errors for debugging
    console.error('Error in swear tracking:', error);
  }
}


